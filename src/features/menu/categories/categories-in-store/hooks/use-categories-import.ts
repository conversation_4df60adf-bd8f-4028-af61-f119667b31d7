import { useState, useRef } from 'react'

import { ParsedCategoryData } from '@/types/categories'
import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import { useBulkCreateItemTypes } from '@/hooks/api'

export function useCategoriesInStoreImport() {
  const [importSelectedFile, setImportSelectedFile] = useState<File | null>(null)
  const [importParsedData, setImportParsedData] = useState<ParsedCategoryData[]>([])
  const [showImportParsedData, setShowImportParsedData] = useState(false)
  const importFileInputRef = useRef<HTMLInputElement>(null)

  const bulkImportCategoriesMutation = useBulkCreateItemTypes()

  const resetImportState = () => {
    setShowImportParsedData(false)
    setImportParsedData([])
    setImportSelectedFile(null)
  }

  const handleDownloadTemplate = () => {
    // Download template file
    const link = document.createElement('a')
    link.href = '/files/categories/categories-in-store/import_item_type_template.xlsx'
    link.download = 'import_item_type_template.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleImportFileUpload = () => {
    importFileInputRef.current?.click()
  }

  const parseExcelFile = async (file: File): Promise<ParsedCategoryData[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = e => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]

          // Convert to JSON, starting from row 2 (skip header)
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: ['name', 'id'],
            range: 1 // Skip first row (header)
          })

          const parsedData: ParsedCategoryData[] = jsonData
            .filter((row: any) => row.name && row.id) // Filter out empty rows
            .map((row: any) => ({
              name: String(row.name).trim(),
              id: String(row.id).trim()
            }))

          if (parsedData.length === 0) {
            toast.error('File không có dữ liệu hợp lệ')
            reject(new Error('No valid data'))
            return
          }

          resolve(parsedData)
        } catch (error) {
          toast.error('Lỗi khi đọc file Excel')
          reject(error)
        }
      }

      reader.onerror = () => {
        toast.error('Lỗi khi đọc file')
        reject(new Error('File read error'))
      }

      reader.readAsArrayBuffer(file)
    })
  }

  const handleImportFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      toast.error('Vui lòng chọn file Excel (.xlsx hoặc .xls)')
      return
    }

    setImportSelectedFile(file)

    try {
      const parsedData = await parseExcelFile(file)
      setImportParsedData(parsedData)
      setShowImportParsedData(true)
      toast.success(`Đã phân tích ${parsedData.length} nhóm món tại cửa hàng từ file!`)
    } catch (error) {
      // Error handling is done in parseExcelFile
      setImportSelectedFile(null)
    }
  }

  const handleSaveImportedCategories = async () => {
    if (importParsedData.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return false
    }

    try {
      // Convert to format expected by bulk create API
      const categories = importParsedData.map((item, index) => ({
        item_type_name: item.name,
        item_type_id: item.id,
        sort: index + 1
      }))

      await bulkImportCategoriesMutation.mutateAsync(categories)
      toast.success(`Đã tạo thành công ${importParsedData.length} nhóm món tại cửa hàng!`)
      resetImportState()
      return true
    } catch (error) {
      toast.error('Lỗi khi tạo nhóm món tại cửa hàng. Vui lòng thử lại.')
      return false
    }
  }

  return {
    importSelectedFile,
    importParsedData,
    showImportParsedData,
    importFileInputRef,
    resetImportState,
    handleDownloadTemplate,
    handleImportFileUpload,
    handleImportFileChange,
    handleSaveImportedCategories,
    isLoading: bulkImportCategoriesMutation.isPending
  }
}
