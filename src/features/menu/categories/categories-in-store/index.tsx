import { useState, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { toast } from 'sonner'

import { ItemType } from '@/lib/item-types-api'

import {
  useCreateItemCategory,
  useDeleteItemType,
  useUpdateItemTypeStatus,
  useStoresData
} from '@/hooks/api'

import { ConfirmModal, SkeletonTable } from '@/components/pos'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

import { categoryColumns, CategoryDataTable, ActionBar } from './components'
import { useItemTypesInStoreData } from './hooks'

function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  if (typeof error === 'string') {
    return error
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }
  return 'Đã xảy ra lỗi không xác định'
}

export default function CategoriesInStorePage() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<ItemType | null>(null)
  const [selectedStoreUid, setSelectedStoreUid] = useState<string>('')

  const { data: storesData = [] } = useStoresData()

  useEffect(() => {
    if (storesData.length > 0 && !selectedStoreUid) {
      const firstActiveStore = storesData.find(store => store.isActive)
      if (firstActiveStore) {
        setSelectedStoreUid(firstActiveStore.id)
      }
    }
  }, [storesData, selectedStoreUid])

  const storeUid = selectedStoreUid

  const {
    data: categories,
    isLoading,
    error
  } = useItemTypesInStoreData({
    search: searchTerm || undefined,
    store_uid: storeUid,
    enabled: true
  })

  const createCategoryMutation = useCreateItemCategory()
  const updateStatusMutation = useUpdateItemTypeStatus()
  const deleteCategoryMutation = useDeleteItemType()

  const handleCreateCategory = () => {
    navigate({ to: '/menu/categories/categories-in-store/detail' })
  }

  const handleToggleStatus = async (category: ItemType) => {
    try {
      const updatedCategory = {
        ...category,
        active: category.active === 1 ? 0 : 1
      }

      await updateStatusMutation.mutateAsync(updatedCategory)
      const statusText = updatedCategory.active === 1 ? 'kích hoạt' : 'vô hiệu hóa'
      toast.success(`${statusText} nhóm "${category.item_type_name}" thành công!`)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleEditCategory = (category: ItemType) => {
    navigate({ to: '/menu/categories/categories-in-store/detail/$id', params: { id: category.id } })
  }

  const handleRowClick = (category: ItemType) => {
    navigate({ to: '/menu/categories/categories-in-store/detail/$id', params: { id: category.id } })
  }

  const handleDeleteCategory = (category: ItemType) => {
    setSelectedCategory(category)
    setConfirmModalOpen(true)
  }

  const handleConfirmDelete = async () => {
    if (!selectedCategory) return

    try {
      await deleteCategoryMutation.mutateAsync(selectedCategory.id)
      toast.success(`Xóa nhóm "${selectedCategory.item_type_name}" thành công!`)
      setConfirmModalOpen(false)
      setSelectedCategory(null)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const storeOptions = storesData.map(store => ({
    value: store.id,
    label: store.name
  }))

  return (
    <div className='container mx-auto px-4 py-8'>
      <ActionBar
        searchQuery={searchQuery}
        onSearchQueryChange={setSearchQuery}
        onSearchSubmit={() => setSearchTerm(searchQuery)}
        onCreateCategory={handleCreateCategory}
        isCreating={createCategoryMutation.isPending}
        selectedStoreUid={selectedStoreUid}
        onStoreChange={setSelectedStoreUid}
        storeOptions={storeOptions}
      />

      {error && (
        <div className='py-8 text-center'>
          <p className='text-red-600'>{getErrorMessage(error)}</p>
        </div>
      )}

      {isLoading && <SkeletonTable rows={8} columns={5} />}

      {!error && !isLoading && (
        <CategoryDataTable
          columns={categoryColumns}
          data={categories || []}
          onEditCategory={handleEditCategory}
          onDeleteCategory={handleDeleteCategory}
          onToggleStatus={handleToggleStatus}
          onRowClick={handleRowClick}
        />
      )}

      <ConfirmModal
        open={confirmModalOpen}
        onOpenChange={setConfirmModalOpen}
        content={
          selectedCategory
            ? `Bạn có muốn xóa nhóm "${selectedCategory.item_type_name}"?`
            : 'Bạn có muốn xóa nhóm này?'
        }
        confirmText='Xác nhận'
        onConfirm={handleConfirmDelete}
        isLoading={deleteCategoryMutation.isPending}
      />
    </div>
  )
}
